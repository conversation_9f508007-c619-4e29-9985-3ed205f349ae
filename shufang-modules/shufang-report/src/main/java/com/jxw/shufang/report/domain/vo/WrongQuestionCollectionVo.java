package com.jxw.shufang.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WrongQuestionCollectionVo {

    @ExcelIgnore
    private Long branchId;

    @ExcelProperty(value = "门店名称")
    private String branchName;

    @ExcelIgnore
    private Long studentId;

    @ExcelProperty(value = "会员名称")
    private String studentName;

    @ExcelIgnore
    private Long consultantId;

    @ExcelProperty(value = "会员顾问")
    private String consultantName;

    @ExcelIgnore
    private Long wrongQuestionCollectionId;

    @ExcelIgnore
    private Integer collectionStatus;

    @ExcelProperty(value = "打印类型")
    private Integer collectionType;

    @ExcelProperty(value = "错题数量")
    private Integer wrongCount;

    @ExcelProperty(value = "订正正确率")
    private BigDecimal rightRate;

    @ExcelProperty(value = "错题评语")
    private String remark;

    @ExcelProperty(value = "打印时间")
    private Date createTime;

}

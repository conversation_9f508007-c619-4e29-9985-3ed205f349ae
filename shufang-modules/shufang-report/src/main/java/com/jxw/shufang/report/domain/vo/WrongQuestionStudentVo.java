package com.jxw.shufang.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WrongQuestionStudentVo {

    @ExcelIgnore
    private Long branchId;

    @ExcelProperty(value = "门店名称")
    private String branchName;

    @ExcelIgnore
    private Long studentId;

    @ExcelProperty(value = "会员名称")
    private String studentName;

    @ExcelIgnore
    private Long consultantId;

    @ExcelProperty(value = "会员顾问")
    private String consultantName;

    @ExcelIgnore
    private Integer sex;

    @ExcelProperty(value = "性别")
    private String sexStr;

    @ExcelProperty(value = "年级")
    private String grade;


    /**
     * 上周错题数量
     */
    @ExcelProperty(value = "上周错题数量")
    private Integer lastWeekWrongCount = 0;

    /**
     * 错题总数
     */
    @ExcelProperty(value = "错题总数")
    private Integer wrongTotal = 0;

    /**
     * 是否打印上周错题
     */
    @ExcelIgnore
    private Integer printLastWeek = 0;

    @ExcelProperty(value = "是否打印上周错题")
    private String printLastWeekStr;

    /**
     * 上周订正数量
     */
    @ExcelProperty(value = "订正上周错题")
    private Integer lastWeekReviseCount = 0;

    /**
     * 订正总数
     */
    @ExcelProperty(value = "订正总数")
    private Integer reviseTotal = 0;

    /**
     * 最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    private Date lastLoginTime;

}

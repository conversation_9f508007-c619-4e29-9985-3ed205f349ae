package com.jxw.shufang.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.report.domain.bo.WrongQuestionStatisticsBo;
import com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.report.domain.vo.WrongQuestionRecordVo;
import com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 错题统计Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Mapper
public interface WrongQuestionStatisticsMapper {

    IPage<WrongQuestionStudentVo> selectWrongQuestionOfStudent(@Param("page") Page<WrongQuestionStudentVo> page,
                                                               @Param("bo") WrongQuestionStatisticsBo bo);

    List<WrongQuestionStudentVo> selectWrongQuestionCount(@Param("studentIds") List<Long> studentIds,
                                                          @Param("start") Date start,
                                                          @Param("end") Date end);

    /**
     * 分页查询学生基本信息
     *
     * @param bo 参数
     * @param studentIds 学生ID列表
     * @return 分页结果
     */
    List<WrongQuestionStudentVo> selectStudentBasicInfoPage(@Param("studentIds") List<Long> studentIds);

    /**
     * 分页查询错题合集统计
     *
     * @param page 分页参数
     * @param bo 查询条件
     * @return 分页结果
     */
    Page<WrongQuestionCollectionVo> selectWrongQuestionCollectionPage(@Param("page") Page<WrongQuestionCollectionVo> page,
                                                                      @Param("bo") WrongQuestionStatisticsBo bo);

    List<WrongQuestionRecordVo> selectDoNotPrintLastWeekCollection(@Param("start") Date start,
                                                                   @Param("end") Date end,
                                                                   @Param("studentIds") List<Long> studentIds);
}

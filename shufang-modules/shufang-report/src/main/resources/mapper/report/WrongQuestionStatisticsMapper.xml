<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.report.mapper.WrongQuestionStatisticsMapper">

    <resultMap id="WrongQuestionStudentResult" type="com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo">
        <result property="branchId" column="branch_id"/>
        <result property="branchName" column="branch_name"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="consultantId" column="consultant_id"/>
        <result property="consultantName" column="consultant_name"/>
        <result property="grade" column="grade"/>
        <result property="sex" column="sex"/>
        <result property="lastLoginTime" column="last_login_time"/>
    </resultMap>

    <select id="selectWrongQuestionOfStudent" resultMap="WrongQuestionStudentResult">
        SELECT
            s.branch_id,
            br.branch_name,
            s.student_id,
            s.student_name,
            bs.branch_staff_id as consultant_id,
            su.nick_name as consultant_name,
            s.student_grade as grade,
            s.student_sex as sex,
            login_user.login_date as last_login_time
        FROM student s
                 INNER JOIN student_consultant_record scr ON s.student_consultant_record_id = scr.student_consultant_record_id
                 INNER JOIN branch_staff bs ON scr.student_consultant_id = bs.branch_staff_id
                 INNER JOIN sys_user su ON bs.create_by = su.user_id
                 INNER JOIN sys_user login_user ON login_user.user_name = s.student_account
                 INNER JOIN branch br ON br.branch_id = s.branch_id
        <where>
            EXISTS ( SELECT 1 FROM wrong_question_record wqr WHERE wqr.student_id = s.student_id )
            <if test="bo.branchId != null">
                AND s.branch_id = #{bo.branchId}
            </if>
            <if test="bo.studentName != null and bo.studentName != ''">
                AND s.student_name LIKE CONCAT('%', #{bo.studentName}, '%')
            </if>
            <if test="bo.consultantName != null and bo.consultantName != ''">
                AND su.nick_name LIKE CONCAT('%', #{bo.consultantName}, '%')
            </if>
        </where>
        order by s.create_time DESC
    </select>

    <select id="selectWrongQuestionCount" resultMap="WrongQuestionStudentResult">
        SELECT
            t.student_id,
            count(DISTINCT question_id) as wrong_total,
            sum(CASE WHEN revise_status = 1 THEN 1 ELSE 0 END) as revise_total
        FROM
            (select distinct student_id, question_id, revise_status from wrong_question_record
                where student_id in
                <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
                <if test="start != null">
                    AND create_time >= #{start}
                </if>
                <if test="start != null">
                    AND create_time &lt;= #{end}
                </if>
            ) t
        GROUP BY
            t.student_id
    </select>

    <!-- 分页查询学生基本信息 -->
    <select id="selectStudentBasicInfoPage" resultMap="WrongQuestionStudentResult">
        SELECT
            s.branch_id,
            b.branch_name,
            s.student_id,
            s.student_name,
            bs.branch_staff_id as consultant_id,
            su.nick_name as consultant_name,
            s.student_grade as grade,
            s.student_sex as sex,
            login_user.login_date as last_login_time
        FROM student s
        LEFT JOIN branch b ON s.branch_id = b.branch_id
        LEFT JOIN student_consultant_record scr ON s.student_consultant_record_id = scr.student_consultant_record_id
        LEFT JOIN branch_staff bs ON scr.student_consultant_id = bs.branch_staff_id
        LEFT JOIN sys_user su ON bs.create_by = su.user_id
        LEFT JOIN sys_user login_user ON s.student_account = login_user.user_name
        WHERE s.student_id IN
        <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </select>

    <resultMap id="WrongQuestionCollectionResult" type="com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo">
        <result property="branchId" column="branch_id"/>
        <result property="branchName" column="branch_name"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="consultantId" column="consultant_id"/>
        <result property="consultantName" column="consultant_name"/>
        <result property="collectionStatus" column="collection_status"/>
        <result property="collectionType" column="collection_type"/>
        <result property="wrongQuestionCollectionId" column="wrong_question_collection_id"/>
        <result property="wrongCount" column="wrong_count"/>
        <result property="rightRate" column="rightRate"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectWrongQuestionCollectionPage" resultMap="WrongQuestionCollectionResult">
        SELECT
            wqc.wrong_question_collection_id,
            wqc.student_id,
            wqc.collection_status,
            wqc.collection_type,
            count(1) as wrong_count,
            sum(case when wqcd.answer_result = '1' then 1 else 0 end) / count(1) rightRate,
            wqc.remark,
            wqc.create_time
        FROM
            wrong_question_collection wqc
            INNER JOIN wrong_question_collection_detail wqcd ON wqcd.wrong_question_collection_id = wqc.wrong_question_collection_id
            <where>
                EXISTS ( SELECT 1 FROM student s
                    INNER JOIN branch b ON s.branch_id = b.branch_id
                    INNER JOIN student_consultant_record scr ON s.student_consultant_record_id = scr.student_consultant_record_id
                    INNER JOIN branch_staff bs ON scr.student_consultant_id = bs.branch_staff_id
                    INNER JOIN sys_user su ON bs.create_by = su.user_id
                    <where>
                        s.student_id = wqc.student_id
                        <if test="bo.branchId != null">
                            AND s.branch_id = #{bo.branchId}
                        </if>
                        <if test="bo.studentName != null and bo.studentName != ''">
                            AND s.student_name LIKE CONCAT('%', #{bo.studentName}, '%')
                        </if>
                        <if test="bo.consultantName != null and bo.consultantName != ''">
                            AND su.nick_name LIKE CONCAT('%', #{bo.consultantName}, '%')
                        </if>
                    </where> )
                <if test="bo.reviseStatus != null">
                    AND wqc.collection_status = #{bo.reviseStatus}
                </if>
            </where>
            GROUP BY
                wqc.wrong_question_collection_id
            ORDER BY wqc.create_time DESC
    </select>

    <select id="selectDoNotPrintLastWeekCollection" resultType="com.jxw.shufang.report.domain.vo.WrongQuestionRecordVo">
        SELECT
            wqr.student_id,
            wqr.question_id
        FROM
            wrong_question_record wqr
        WHERE
            wqr.create_time >= #{start} AND wqr.create_time &lt;= #{end}
            AND NOT EXISTS (
                SELECT 1 FROM wrong_question_collection wqc
                    INNER JOIN wrong_question_collection_detail wqcd ON wqcd.wrong_question_collection_id = wqc.wrong_question_collection_id
                WHERE wqc.student_id = wqr.student_id
                    AND wqcd.question_id = wqr.question_id
                    AND wqc.collection_type = 2
                )
            <if test="studentIds != null and studentIds.size > 0">
                and wqr.student_id in
                <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY wqr.student_id, wqr.question_id
    </select>

</mapper>

package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.extresource.api.RemoteKnowledgeService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordNewBo;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupVideoVo;
import com.jxw.shufang.student.domain.WrongQuestionRecord;
import com.jxw.shufang.student.domain.bo.CourseBo;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordBo;
import com.jxw.shufang.student.mapper.WrongQuestionRecordMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 错题记录Service业务层处理
 *
 * @date 2024-05-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WrongQuestionRecordServiceImpl implements IWrongQuestionRecordService, BaseService {

    private final WrongQuestionRecordMapper baseMapper;

    private final IStudyPlanningRecordService planningRecordService;

    private final IWrongQuestionCollectionDetailService wrongQuestionCollectionDetailService;

    @Autowired
    @Lazy
    private IWrongQuestionRecordNewService wrongQuestionRecordNewService;

    @Autowired
    @Lazy
    private ICorrectionRecordService correctionRecordService;

    private final ICourseService courseService;

    @DubboReference
    private RemoteQuestionService remoteQuestionService;
    @DubboReference
    private RemoteKnowledgeService remoteKnowledgeService;
    @DubboReference
    private RemoteFileService ossService;

    /**
     * 查询错题记录
     */
    @Override
    public WrongQuestionRecordVo queryById(Long wrongQuestionRecordId) {
        return baseMapper.selectVoById(wrongQuestionRecordId);
    }

    /**
     * 查询错题记录列表
     */
    @Override
    public TableDataInfo<WrongQuestionRecordVo> selectWrongQuestionRecordPage(WrongQuestionRecordBo bo, PageQuery pageQuery) {
        QueryWrapper<WrongQuestionRecord> lqw = buildQueryWrapper(bo);
        Page<WrongQuestionRecordVo> result = baseMapper.selectWrongQuestionRecordPage(pageQuery.build(), lqw);
        //拿出所有的课程，去找到各自的顶级课程
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<CourseVo> courseVos = result.getRecords().stream().map(WrongQuestionRecordVo::getCourse).filter(Objects::nonNull).toList();
            courseService.putTopmostCourseInfo(courseVos, Boolean.TRUE);
            if (CollUtil.isNotEmpty(courseVos)) {
                List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).toList();
                courseService.putCourseDetail(list, Boolean.FALSE);
            }
        }
        List<WrongQuestionRecordVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> questionIdList = records.stream().map(WrongQuestionRecordVo::getQuestionId).distinct().toList();
            RemoteQuestionVideoBo remoteQuestionVideoBo = new RemoteQuestionVideoBo();
            remoteQuestionVideoBo.setQuestionIdList(questionIdList);
            List<RemoteGroupVideoVo> questionVideoList = remoteQuestionService.getQuestionVideoList(remoteQuestionVideoBo);
            if (CollUtil.isNotEmpty(questionVideoList)) {
                Map<Long, RemoteGroupVideoVo> questionVideoMap = questionVideoList.stream().collect(Collectors.toMap(RemoteGroupVideoVo::getQuestionId, item -> item));
                for (WrongQuestionRecordVo wrongQuestionRecordVo : records) {
                    RemoteGroupVideoVo remoteGroupVideoVo = questionVideoMap.get(wrongQuestionRecordVo.getQuestionId());
                    if (remoteGroupVideoVo != null && CollUtil.isNotEmpty(remoteGroupVideoVo.getVideoVoList())) {
                        wrongQuestionRecordVo.setQuestionVideo(remoteGroupVideoVo.getVideoVoList().get(0));
                    }
                }
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询错题记录列表
     */
    @Override
    public List<WrongQuestionRecordVo> queryList(WrongQuestionRecordBo bo) {
        LambdaQueryWrapper<WrongQuestionRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WrongQuestionRecord> buildLambdaQueryWrapper(WrongQuestionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WrongQuestionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, WrongQuestionRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, WrongQuestionRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(bo.getQuestionId() != null, WrongQuestionRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), WrongQuestionRecord::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), WrongQuestionRecord::getAnswerResult, bo.getAnswerResult());
        lqw.eq(bo.getCourseId() != null, WrongQuestionRecord::getCourseId, bo.getCourseId());
        lqw.in(CollUtil.isNotEmpty(bo.getQuestionIds()), WrongQuestionRecord::getQuestionId, bo.getQuestionIds());

        if (StringUtils.isNotBlank(bo.getCourseName()) || StringUtils.isNotBlank(bo.getSpecialTopic()) || StringUtils.isNotBlank(bo.getAttrSearchJson()) || StringUtils.isNotBlank(bo.getAffiliationSubject())) {
            CourseBo courseBo = new CourseBo();
            courseBo.setCourseName(bo.getCourseName());
            courseBo.setSpecialTopic(bo.getSpecialTopic());
            courseBo.setAttrSearchJson(bo.getAttrSearchJson());
            courseBo.setAffiliationSubject(bo.getAffiliationSubject());
            List<CourseChildInfoVo> childInfoList = courseService.getChildInfoList(courseBo);
            if (CollUtil.isEmpty(childInfoList)) {
                lqw.apply("1=2");
            } else {
                List<Long> childIds = childInfoList.stream().map(CourseChildInfoVo::getChildIds).filter(StringUtils::isNotBlank).flatMap(item -> Arrays.stream(item.split(","))).map(Long::parseLong).distinct().toList();
                if (CollUtil.isEmpty(childIds)) {
                    lqw.apply("1=2");
                } else {
                    lqw.in(WrongQuestionRecord::getCourseId, childIds);
                }
            }
        }
        return lqw;
    }

    private QueryWrapper<WrongQuestionRecord> buildQueryWrapper(WrongQuestionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<WrongQuestionRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, "t.study_planning_record_id", bo.getStudyPlanningRecordId());
        lqw.eq(bo.getQuestionId() != null, "t.question_id", bo.getQuestionId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), "t.question_no", bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), "t.answer_result", bo.getAnswerResult());
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(bo.getReviseStatus() != null, "t.revise_status", bo.getReviseStatus());
        lqw.between(Objects.nonNull(bo.getStart()) && Objects.nonNull(bo.getEnd()), "t.create_time", bo.getStart(), bo.getEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getWrongQuestionRecordIdList()), "t.wrong_question_record_id", bo.getWrongQuestionRecordIdList());
        lqw.between(bo.getRecordCreateTimeStart() != null && bo.getRecordCreateTimeEnd() != null,
            "t.create_time", bo.getRecordCreateTimeStart(), bo.getRecordCreateTimeEnd());
        if (StringUtils.isNotBlank(bo.getCourseName()) || StringUtils.isNotBlank(bo.getSpecialTopic()) || StringUtils.isNotBlank(bo.getAttrSearchJson()) || StringUtils.isNotBlank(bo.getAffiliationSubject())) {
            CourseBo courseBo = new CourseBo();
            courseBo.setCourseName(bo.getCourseName());
            courseBo.setSpecialTopic(bo.getSpecialTopic());
            courseBo.setAttrSearchJson(bo.getAttrSearchJson());
            courseBo.setAffiliationSubject(bo.getAffiliationSubject());
            List<CourseChildInfoVo> childInfoList = courseService.getChildInfoList(courseBo);

            if (CollUtil.isEmpty(childInfoList)) {
                lqw.apply("1=2");
            } else {
                List<Long> childIds = childInfoList.stream().map(CourseChildInfoVo::getChildIds).filter(StringUtils::isNotBlank).flatMap(item -> Arrays.stream(item.split(","))).map(Long::parseLong).distinct().toList();
                if (CollUtil.isEmpty(childIds)) {
                    lqw.apply("1=2");
                } else {
                    lqw.in("t.course_id", childIds);
                }
            }
        }
        // 添加分组和统计逻辑
//        lqw.orderByDesc("latest_time"); // 按最新时间降序排序
        return lqw;
    }


    /**
     * 新增错题记录
     */
    @Override
    public Boolean insertByBo(WrongQuestionRecordBo bo) {
        WrongQuestionRecord add = MapstructUtils.convert(bo, WrongQuestionRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWrongQuestionRecordId(add.getWrongQuestionRecordId());
        }
        return flag;
    }

    /**
     * 修改错题记录
     */
    @Override
    public Boolean updateByBo(WrongQuestionRecordBo bo) {
        WrongQuestionRecord update = MapstructUtils.convert(bo, WrongQuestionRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean batchUpdate(List<WrongQuestionRecordVo> voList) {
        List<WrongQuestionRecord> convert = MapstructUtils.convert(voList, WrongQuestionRecord.class);
        return baseMapper.updateBatchById(convert);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WrongQuestionRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除错题记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<WrongQuestionRecordBo> collect) {
        List<WrongQuestionRecord> convert = MapstructUtils.convert(collect, WrongQuestionRecord.class);
        return baseMapper.insertBatch(convert);
    }

    @Override
    public TableDataInfo<WrongQuestionGroupVo> listGroupByDate(Long studentId, String affiliationSubject, PageQuery pageQuery) {
        return GroupByDate(null, null, studentId, affiliationSubject, pageQuery);
    }

    private @NotNull TableDataInfo<WrongQuestionGroupVo> GroupByDate(String recordCreateTimeStart, String recordCreateTimeEnd,
                                                                     Long studentId, String affiliationSubject, PageQuery pageQuery) {
        List<Long> courseIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(affiliationSubject)) {
            CourseBo courseBo = new CourseBo();
            courseBo.setAffiliationSubject(affiliationSubject);
            List<CourseChildInfoVo> childInfoList = courseService.getChildInfoList(courseBo);
            if (CollUtil.isEmpty(childInfoList)) {
                courseIdList.add(-1L);
            } else {
                List<Long> childIds = childInfoList.stream().map(CourseChildInfoVo::getChildIds).filter(StringUtils::isNotBlank).flatMap(item -> Arrays.stream(item.split(","))).map(Long::parseLong).distinct().toList();
                if (CollUtil.isEmpty(childIds)) {
                    courseIdList.add(-1L);
                } else {
                    courseIdList.addAll(childIds);
                }
            }
        }
        Page<WrongQuestionGroupVo> result = baseMapper.listGroupByDateV2(recordCreateTimeStart, recordCreateTimeEnd, studentId, courseIdList, pageQuery.build());
        List<WrongQuestionGroupVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            //查询对应分组的错误题目列表
            List<Long> idList = records.stream().map(WrongQuestionGroupVo::getWrongQuestionRecordIdPlural).flatMap(item -> Arrays.stream(item.split(","))).map(Long::parseLong).distinct().toList();
            LambdaQueryWrapper<WrongQuestionRecord> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.in(WrongQuestionRecord::getWrongQuestionRecordId, idList);
            List<WrongQuestionRecordVo> wrongQuestionRecordVos = baseMapper.selectVoList(lambdaQuery);

            //查询错误题目对应的题目解析视频
            List<Long> questionIdList = wrongQuestionRecordVos.stream().map(WrongQuestionRecordVo::getQuestionId).distinct().toList();
            RemoteQuestionVideoBo remoteQuestionVideoBo = new RemoteQuestionVideoBo();
            remoteQuestionVideoBo.setQuestionIdList(questionIdList);
            List<RemoteGroupVideoVo> questionVideoList = remoteQuestionService.getQuestionVideoList(remoteQuestionVideoBo);
            if (CollUtil.isNotEmpty(questionVideoList)) {
                Map<Long, RemoteGroupVideoVo> questionVideoMap = questionVideoList.stream().collect(Collectors.toMap(RemoteGroupVideoVo::getQuestionId, item -> item));
                for (WrongQuestionRecordVo wrongQuestionRecordVo : wrongQuestionRecordVos) {
                    RemoteGroupVideoVo remoteGroupVideoVo = questionVideoMap.get(wrongQuestionRecordVo.getQuestionId());
                    if (remoteGroupVideoVo != null && CollUtil.isNotEmpty(remoteGroupVideoVo.getVideoVoList())) {
                        wrongQuestionRecordVo.setQuestionVideo(remoteGroupVideoVo.getVideoVoList().get(0));
                    }
                }
            }

            //查询错误题目对应的课程章节信息
            List<Long> studyPlanningRecordIdList = wrongQuestionRecordVos.stream().map(WrongQuestionRecordVo::getStudyPlanningRecordId).toList();
            List<StudyPlanningRecordVo> courseListByRecordIdList = planningRecordService.getCourseInfoByRecordIdList(studyPlanningRecordIdList);

            //拿出所有的课程，去找到各自的顶级课程
            if (CollUtil.isNotEmpty(courseListByRecordIdList)) {
                List<CourseVo> courseVos = courseListByRecordIdList.stream().map(StudyPlanningRecordVo::getCourse).toList();
                courseService.putTopmostCourseInfo(courseVos, Boolean.FALSE);
            }

            //开始拼装数据
            //处理错误题目和课程信息的对应关系
            Map<Long, CourseVo> map = StreamUtils.toMap(courseListByRecordIdList, StudyPlanningRecordVo::getStudyPlanningRecordId, StudyPlanningRecordVo::getCourse);
            for (WrongQuestionRecordVo wrongQuestionRecordVo : wrongQuestionRecordVos) {
                Long studyPlanningRecordId = wrongQuestionRecordVo.getStudyPlanningRecordId();
                CourseVo courseVo = map.get(studyPlanningRecordId);
                if (courseVo != null) {
                    wrongQuestionRecordVo.setCourse(courseVo);
                }
            }

            //处理错误题目和错误题组的对应关系
            Map<Long, WrongQuestionRecordVo> wrongQuestionRecordMap = StreamUtils.toMap(wrongQuestionRecordVos, WrongQuestionRecordVo::getWrongQuestionRecordId, Function.identity());
            for (WrongQuestionGroupVo record : records) {
                String wrongQuestionRecordIdPlural = record.getWrongQuestionRecordIdPlural();
                if (StringUtils.isNotBlank(wrongQuestionRecordIdPlural)) {
                    List<Long> list = Arrays.stream(wrongQuestionRecordIdPlural.split(",")).map(Long::parseLong).toList();
                    List<WrongQuestionRecordVo> wrongQuestionRecordVoList = list.stream().map(wrongQuestionRecordMap::get).filter(Objects::nonNull).toList();
                    if (CollUtil.isNotEmpty(wrongQuestionRecordVoList)) {
                        //wrongQuestionRecordVoList按照sourceType从小到大排序，questionNo从小到大排序
                        // 按照 sourceType 从小到大排序，然后在每个 sourceType 分组内按照 questionNo 从小到大排序
                        List<WrongQuestionRecordVo> sortedList = wrongQuestionRecordVoList.stream()
                            .sorted(Comparator.comparing(WrongQuestionRecordVo::getSourceType)
                                .thenComparingDouble(WrongQuestionRecordVo::takeQuestionNo))
                            .toList();
                        record.setWrongQuestionRecordVoList(sortedList);
                    }
                }
            }
        }
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<WrongQuestionGroupVo> listGroupByDateV2(String recordCreateTimeStart, String recordCreateTimeEnd, Long studentId, String affiliationSubject, PageQuery pageQuery) {
        return GroupByDate(recordCreateTimeStart, recordCreateTimeEnd, studentId, affiliationSubject, pageQuery);
    }

    @Override
    public TableDataInfo<WrongQuestionRecordV2Vo> selectWrongQuestionRecordPageV2(WrongQuestionRecordBo bo, PageQuery pageQuery) {
        QueryWrapper<WrongQuestionRecord> lqw = buildQueryWrapper(bo);

        Page<WrongQuestionRecordV2Vo> result = baseMapper.selectRecordPageGroupByStudent(pageQuery.build(), lqw);
        //拿出所有的课程，去找到各自的顶级课程
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<CourseVo> courseVos = result.getRecords().stream().map(WrongQuestionRecordV2Vo::getCourse).filter(Objects::nonNull).toList();
            courseService.putTopmostCourseInfo(courseVos, Boolean.TRUE);
            if (CollUtil.isNotEmpty(courseVos)) {
                List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).toList();
                courseService.putCourseDetail(list, Boolean.FALSE);
            }
        }
        List<WrongQuestionRecordV2Vo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfo.build(result);
        }

        List<Long> questionIdList = records.stream().map(WrongQuestionRecordV2Vo::getQuestionId).distinct().toList();
        setRemoteQuestion(questionIdList, records);
        setRemoteQuestionVideo(records);

        records.forEach(i -> {
            if (StringUtils.isNotBlank(i.getReviseScreenshots())) {
                String urls = ossService.selectUrlByIds(i.getReviseScreenshots());
                if (StringUtils.isNotBlank(urls)) {
                    i.setReviseScreenshotUrls(List.of(urls.split(",")));
                }
            }
        });

        return TableDataInfo.build(result);
    }

    private void setRemoteQuestion(List<Long> questionIdList, List<WrongQuestionRecordV2Vo> records) {
        List<RemoteQuestionVo> remoteQuestionVos = remoteQuestionService.listQuestions(questionIdList);
        if (CollUtil.isEmpty(remoteQuestionVos)) {
            return;
        }
        List<Long> list = remoteQuestionVos.stream().map(RemoteQuestionVo::getPid).filter(pid -> !(ObjectUtils.isEmpty(pid) || pid == 0)).toList();
        if (CollUtil.isNotEmpty(list)) {
            List<RemoteQuestionVo> parentQuestionList = remoteQuestionService.listQuestions(list);
            if (CollUtil.isNotEmpty(parentQuestionList)) {
                remoteQuestionVos.addAll(parentQuestionList);
            }
        }

        Map<Long, RemoteQuestionVo> questionIdMap = remoteQuestionVos.stream().collect(Collectors.toMap(RemoteQuestionVo::getId, item -> item));
        for (WrongQuestionRecordV2Vo wrongQuestionRecordVo : records) {
            RemoteQuestionVo remoteQuestionVo = questionIdMap.get(wrongQuestionRecordVo.getQuestionId());
            if (ObjectUtils.isEmpty(remoteQuestionVo)) {
                continue;
            }
            // 设置当前错题Id的父级Id
            wrongQuestionRecordVo.setParentQuestionId(remoteQuestionVo.getPid());
            setQuestion(wrongQuestionRecordVo, remoteQuestionVo, questionIdMap);

        }
    }

    private static void setQuestion(WrongQuestionRecordV2Vo wrongQuestionRecordVo, RemoteQuestionVo remoteQuestionVo, Map<Long, RemoteQuestionVo> questionIdMap) {
        Long pid = remoteQuestionVo.getPid();
        if (ObjectUtils.isEmpty(pid) || pid == 0) {
            wrongQuestionRecordVo.setQuestionVo(remoteQuestionVo);
            return;
        }
        RemoteQuestionVo parentQuestion = questionIdMap.get(pid);
        //由于操作的是同一个父级，需要重新生成每一个父级Question
        RemoteQuestionVo tempQuestion = MapstructUtils.convert(parentQuestion, RemoteQuestionVo.class);
        wrongQuestionRecordVo.setQuestionVo(tempQuestion);
        if (!ObjectUtils.isEmpty(tempQuestion)) {
            //如果有父级则最外面的是父级问题
            tempQuestion.setChildren(Collections.singletonList(remoteQuestionVo));
        }
    }

    private void setRemoteQuestionVideo(List<WrongQuestionRecordV2Vo> records) {
        List<Long> questionIdList = records.stream()
            .map(WrongQuestionRecordV2Vo::getQuestionVo)
            .filter(entity -> !ObjectUtils.isEmpty(entity))
            .map(RemoteQuestionVo::getId)
            .filter(entity -> !ObjectUtils.isEmpty(entity))
            .toList();
        if (CollUtil.isEmpty(questionIdList)) {
            return;
        }
        List<RemoteGroupVideoVo> questionVideoList = getRemoteGroupVideoVos(questionIdList);
        if (CollUtil.isNotEmpty(questionVideoList)) {
            Map<Long, RemoteGroupVideoVo> questionVideoMap = questionVideoList.stream().collect(Collectors.toMap(RemoteGroupVideoVo::getQuestionId, item -> item));
            for (WrongQuestionRecordV2Vo wrongQuestionRecordVo : records) {
                RemoteGroupVideoVo remoteGroupVideoVo = questionVideoMap.get(wrongQuestionRecordVo.getQuestionId());
                if (remoteGroupVideoVo != null && CollUtil.isNotEmpty(remoteGroupVideoVo.getVideoVoList())) {
                    wrongQuestionRecordVo.setQuestionVideo(remoteGroupVideoVo.getVideoVoList().get(0));
                }
            }
        }
    }

    @Override
    public WrongQuestionRecordVo queryByIdV2(Long wrongQuestionRecordId) {
        WrongQuestionRecordVo wrongQuestionRecordVo = baseMapper.selectVoById(wrongQuestionRecordId);

        if (ObjectUtils.isEmpty(wrongQuestionRecordVo)) {
            return wrongQuestionRecordVo;
        }
        Integer wrongCount = baseMapper.wrongCount(wrongQuestionRecordVo.getQuestionId(), wrongQuestionRecordVo.getStudentId());
        wrongQuestionRecordVo.setWrongCount(wrongCount);
        StudyPlanningRecordVo studyPlanningRecordVo = planningRecordService.queryById(wrongQuestionRecordVo.getStudyPlanningRecordId());
        if (!ObjectUtils.isEmpty(studyPlanningRecordVo)) {
            Long studyPlanningRecordId = studyPlanningRecordVo.getStudyPlanningRecordId();
            CorrectionRecordVo correctionRecordVo = correctionRecordService.queryRecord(studyPlanningRecordId, null);
            if (!ObjectUtils.isEmpty(correctionRecordVo)) {
                wrongQuestionRecordVo.setCorrectionScreenshots(correctionRecordVo.getCorrectionScreenshots());
            }
        }
        Long courseId = wrongQuestionRecordVo.getCourseId();
        CourseVo courseVo = courseService.queryById(courseId);
        if (!ObjectUtils.isEmpty(courseVo)) {
            wrongQuestionRecordVo.setCourse(courseVo);
            courseService.putTopmostCourseInfo(Collections.singletonList(courseVo), Boolean.TRUE);
            CourseVo topmostCourse = courseVo.getTopmostCourse();
            if (!ObjectUtils.isEmpty(topmostCourse)) {
                courseService.putCourseDetail(Collections.singletonList(topmostCourse), Boolean.FALSE);
            }
        }


        Long questionId = wrongQuestionRecordVo.getQuestionId();
        if (ObjectUtils.isEmpty(questionId)) {
            return wrongQuestionRecordVo;
        }
        List<RemoteGroupVideoVo> questionVideoList = getRemoteGroupVideoVos(Collections.singletonList(questionId));
        if (CollUtil.isNotEmpty(questionVideoList)) {
            setQuestionVideo(questionVideoList, wrongQuestionRecordVo);
        }
        List<RemoteQuestionVo> remoteQuestionVos = remoteQuestionService.listQuestionsWithKnowledge(Collections.singletonList(questionId));
        if (CollUtil.isNotEmpty(remoteQuestionVos)) {
            setQuestion(remoteQuestionVos, wrongQuestionRecordVo);
        }
        return wrongQuestionRecordVo;
    }

    @Override
    public List<WrongQuestionRecordV2Vo> mergeSubQuestionsByRelation(WrongQuestionRecordBo bo, PageQuery pageQuery, Boolean isRelevancy) {
        List<WrongQuestionRecordV2Vo> dataInfoRowList;
        if (Objects.nonNull(bo.getWrongQuestionCollectionId())) {
            dataInfoRowList = queryByWrongQuestionCollection(bo);
        } else {
            TableDataInfo<WrongQuestionRecordV2Vo> wrongQuestionRecordV2VoTableDataInfo = this.selectWrongQuestionRecordPageV2(bo, pageQuery);
            dataInfoRowList = wrongQuestionRecordV2VoTableDataInfo.getRows();
            if (Objects.nonNull(bo.getMaxProcessCount()) && dataInfoRowList.size() >= bo.getMaxProcessCount()) {
                log.info("错题数量超过客户端处理能力，不支持打印！参数：{}", bo);
                return null;
            }
        }

        // 合并的时候才抛出异常
        if (isRelevancy) {
            // 如果本次需要关联题目没有子题，提示异常信息
            List<WrongQuestionRecordV2Vo> parentquestionList = dataInfoRowList.stream().filter(vo -> vo.getParentQuestionId() == 0).toList();
            if (dataInfoRowList.size() == parentquestionList.size()) {
                throw new ServiceException("当前无关联子题");
            }
        }

        // 查询出本次需要中有父题目的数据
        Map<Long, List<RemoteQuestionVo>> questionVoMap = new HashMap<>();
        for (WrongQuestionRecordV2Vo record : dataInfoRowList) {
            Long parentQuestionId = record.getParentQuestionId();
            if (parentQuestionId == null || parentQuestionId == 0L) {
                continue;
            }
            // 获取子题目列表
            List<RemoteQuestionVo> childrenQuestionList = record.getQuestionVo().getChildren();
            if (CollUtil.isNotEmpty(childrenQuestionList)) {
                // 如果不包含，则创建一共空数组
                if (!questionVoMap.containsKey(parentQuestionId)) {
                    questionVoMap.put(parentQuestionId, new ArrayList<>());
                }
                // 如果存在那么就添加数组合并
                questionVoMap.get(parentQuestionId).addAll(childrenQuestionList);
            }
        }


        // 把原数据中有父级Id相同的去重
        List<WrongQuestionRecordV2Vo> resultList = new ArrayList<>();
        dataInfoRowList.forEach(wrongQuestionRecordV2Vo -> {
            if (wrongQuestionRecordV2Vo.getParentQuestionId() == 0) {
                resultList.add(wrongQuestionRecordV2Vo);
            } else {
                if (resultList.stream().noneMatch(item -> item.getParentQuestionId().equals(wrongQuestionRecordV2Vo.getParentQuestionId()))) {
                    resultList.add(wrongQuestionRecordV2Vo);
                }
            }
        });

        // 合并子题
        resultList.forEach(wrongQuestionRecordV2Vo -> {
            if (questionVoMap.containsKey(wrongQuestionRecordV2Vo.getParentQuestionId())) {
                List<RemoteQuestionVo> children = questionVoMap.get(wrongQuestionRecordV2Vo.getParentQuestionId());
                if (children != null) {
                    // 合并有小题，那么questionNo就置空，前端用于展示小题序号
                    wrongQuestionRecordV2Vo.setQuestionNo("");
                    wrongQuestionRecordV2Vo.getQuestionVo().setChildren(children);
                }
            }
        });
        // 是否需要关联子题（未做错的也展示）
        if (isRelevancy) {
            List<Long> questionOrParentIds = resultList.stream()
                .flatMap(vo -> {
                    if (vo.getParentQuestionId() == 0) {
                        return Stream.of(vo.getQuestionId());
                    } else {
                        return Stream.of(vo.getParentQuestionId());
                    }
                }).toList();
            List<RemoteQuestionVo> questionVoList = remoteQuestionService.listQuestions(questionOrParentIds);
            Map<Long, RemoteQuestionVo> collect = questionVoList.stream().collect(Collectors.toMap(RemoteQuestionVo::getId, Function.identity()));
            resultList.forEach(vo -> {
                if (vo.getParentQuestionId() == 0) {
                    vo.setQuestionVo(collect.get(vo.getQuestionId()));
                } else {
                    vo.setQuestionVo(collect.get(vo.getParentQuestionId()));
                }
            });
            return resultList;
        }
        return resultList;
    }

    @Override
    public List<WrongQuestionRecordV2Vo> queryByWrongQuestionCollection(WrongQuestionRecordBo bo) {
        List<WrongQuestionCollectionDetailVo> detailVos = wrongQuestionCollectionDetailService.queryByCollectionId(bo.getWrongQuestionCollectionId());
        if (CollUtil.isEmpty(detailVos)) {
            return Collections.emptyList();
        }

        Map<Long, WrongQuestionCollectionDetailVo> detailVoMap = detailVos.stream().collect(
            Collectors.toMap(WrongQuestionCollectionDetailVo::getQuestionId, Function.identity()));
        Set<Long> parentIds = detailVos.stream().map(WrongQuestionCollectionDetailVo::getQuestionPid).collect(Collectors.toSet());
        List<WrongQuestionRecordV2Vo> recordV2VoList = new ArrayList<>();
        detailVos.forEach(i -> {
            WrongQuestionRecordV2Vo vo = new WrongQuestionRecordV2Vo();
            if (!parentIds.contains(i.getQuestionId())) {
                vo.setQuestionId(i.getQuestionId());
                recordV2VoList.add(vo);
            }
        });
        List<Long> questionIds = recordV2VoList.stream().map(WrongQuestionRecordV2Vo::getQuestionId).toList();

        // 设置题目信息
        setRemoteQuestion(questionIds, recordV2VoList);
        recordV2VoList.forEach(i -> {
            RemoteQuestionVo questionVo = i.getQuestionVo();
            if (Objects.nonNull(questionVo)) {
                if (CollUtil.isEmpty(questionVo.getChildren())) {
                    questionVo.setQuestionNo(detailVoMap.get(questionVo.getId()).getQuestionNo());
                } else {
                    questionVo.getChildren().forEach(j ->
                        j.setQuestionNo(detailVoMap.get(j.getId()).getQuestionNo()));
                }
            }
        });

        // 设置题目视频
        setRemoteQuestionVideo(recordV2VoList);
        return recordV2VoList;
    }

    @Transactional
    @Override
    public Boolean reviseAgain(Long wrongQuestionRecordId) {
        WrongQuestionRecordVo wrongQuestionRecordVo = baseMapper.selectVoById(wrongQuestionRecordId);
        if (Objects.isNull(wrongQuestionRecordVo)) {
            return Boolean.FALSE;
        }
        WrongQuestionRecordBo bo = new WrongQuestionRecordBo();
        bo.setStudentId(wrongQuestionRecordVo.getStudentId());
        bo.setQuestionId(wrongQuestionRecordVo.getQuestionId());
        LambdaQueryWrapper<WrongQuestionRecord> wrapper = buildLambdaQueryWrapper(bo);
        List<WrongQuestionRecord> wrongQuestionRecords = baseMapper.selectList(wrapper);
        wrongQuestionRecords.forEach(i -> {
            i.setReviseStatus(0);
            i.setReviseScreenshots("");
        });
        baseMapper.updateBatchById(wrongQuestionRecords);

        WrongQuestionRecordNewBo recordNewBo = new WrongQuestionRecordNewBo();
        recordNewBo.setStudentId(wrongQuestionRecordVo.getStudentId());
        recordNewBo.setQuestionId(wrongQuestionRecordVo.getQuestionId());
        List<WrongQuestionRecordNewVo> recordNewVos = wrongQuestionRecordNewService.queryList(recordNewBo);
        if (CollUtil.isNotEmpty(recordNewVos)) {
            recordNewVos.forEach(i -> {
                i.setReviseStatus(0);
                i.setReviseScreenshots("");
            });
        }
        wrongQuestionRecordNewService.batchUpdate(recordNewVos);

        return Boolean.TRUE;
    }

    private void setQuestion(List<RemoteQuestionVo> questionVoList, WrongQuestionRecordVo wrongQuestionRecordVo) {
        Map<Long, RemoteQuestionVo> questionVideoMap = questionVoList.stream().collect(Collectors.toMap(RemoteQuestionVo::getId, item -> item));
        RemoteQuestionVo remoteQuestionVo = questionVideoMap.get(wrongQuestionRecordVo.getQuestionId());
        if (ObjectUtils.isEmpty(remoteQuestionVo)) {
            return;
        }
        Long pid = remoteQuestionVo.getPid();
        if (ObjectUtils.isEmpty(pid) || pid == 0) {
            wrongQuestionRecordVo.setQuestionVo(remoteQuestionVo);
            return;
        }
        List<RemoteQuestionVo> remoteQuestionVos = remoteQuestionService.listQuestions(Collections.singletonList(pid));
        if (ObjectUtils.isEmpty(remoteQuestionVos)) {
            return;
        }
        RemoteQuestionVo parentQuestion = remoteQuestionVos.get(0);
        parentQuestion.setChildren(Collections.singletonList(remoteQuestionVo));
        //如果有父级则最外面的是父级问题
        wrongQuestionRecordVo.setQuestionVo(parentQuestion);
    }

    private List<RemoteGroupVideoVo> getRemoteGroupVideoVos(List<Long> questionId) {
        RemoteQuestionVideoBo remoteQuestionVideoBo = new RemoteQuestionVideoBo();
        remoteQuestionVideoBo.setQuestionIdList(questionId);
        return remoteQuestionService.getQuestionVideoList(remoteQuestionVideoBo);
    }

    private static void setQuestionVideo(List<RemoteGroupVideoVo> questionVideoList, WrongQuestionRecordVo wrongQuestionRecordVo) {
        Map<Long, RemoteGroupVideoVo> questionVideoMap = questionVideoList.stream().collect(Collectors.toMap(RemoteGroupVideoVo::getQuestionId, item -> item));
        RemoteGroupVideoVo remoteGroupVideoVo = questionVideoMap.get(wrongQuestionRecordVo.getQuestionId());
        if (remoteGroupVideoVo != null && CollUtil.isNotEmpty(remoteGroupVideoVo.getVideoVoList())) {
            wrongQuestionRecordVo.setQuestionVideo(remoteGroupVideoVo.getVideoVoList().get(0));
        }
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}

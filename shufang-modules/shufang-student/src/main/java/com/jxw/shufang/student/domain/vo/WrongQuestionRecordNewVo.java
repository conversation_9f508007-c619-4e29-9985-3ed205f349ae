package com.jxw.shufang.student.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.WrongQuestionRecordNew;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WrongQuestionRecordNew.class)
public class WrongQuestionRecordNewVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 错题id
     */
    @TableId(value = "wrong_question_record_new_id")
    private Long wrongQuestionRecordNewId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 题目ID
     */
    private Long questionId;

    private RemoteQuestionVo question;

    /**
     * 科目ID（cds_subject）
     */
    private Integer subjectId;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 用户答案状态（对应字典值，如全错 半错）
     */
    private String userAnswerStatus;

    /**
     * 来源类型(对应字典值)
     */
    private String sourceType;
    /**
     * 来源ID（学测练为courseId,AI评测为paperId）
     */
    private String sourceId;

    private Object source;

    private Date createTime;

    private Date updateTime;

    /**
     * 订正状态 0-否 1-是
     */
    private Integer reviseStatus;

    /**
     * 订正时间
     */
    private Date reviseTime;

    /**
     * 订正截图（oss_id，多个，逗号隔开)
     */
    private String reviseScreenshots;

    private List<String> reviseScreenshotUrls;

}

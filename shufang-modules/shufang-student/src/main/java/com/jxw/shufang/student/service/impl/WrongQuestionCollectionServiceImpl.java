package com.jxw.shufang.student.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.student.domain.WrongQuestionCollection;
import com.jxw.shufang.student.domain.WrongQuestionCollectionDetail;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.WrongQuestionCollectionMapper;
import com.jxw.shufang.student.service.IWrongQuestionCollectionDetailService;
import com.jxw.shufang.student.service.IWrongQuestionCollectionService;
import com.jxw.shufang.student.service.IWrongQuestionRecordNewService;
import com.jxw.shufang.student.service.IWrongQuestionRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 错题合集Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WrongQuestionCollectionServiceImpl implements IWrongQuestionCollectionService, BaseService {

    private final WrongQuestionCollectionMapper baseMapper;
    private final IWrongQuestionCollectionDetailService wrongQuestionCollectionDetailService;
    private final IWrongQuestionRecordService wrongQuestionRecordService;
    private final IWrongQuestionRecordNewService wrongQuestionRecordNewService;

    @DubboReference
    private RemoteFileService ossService;

    /**
     * 查询错题合集
     */
    @Override
    public WrongQuestionCollectionVo queryById(Long wrongQuestionCollectionId) {
        WrongQuestionCollectionVo vo = baseMapper.selectVoById(wrongQuestionCollectionId);
        if (Objects.nonNull(vo) && StringUtils.isNotBlank(vo.getReviseScreenshots())) {
            String urls = ossService.selectUrlByIds(vo.getReviseScreenshots());
            if (StringUtils.isNotBlank(urls)) {
                vo.setReviseScreenshotUrls(List.of(urls.split(",")));
            }
        }
        return vo;
    }

    /**
     * 查询错题合集列表
     */
    @Override
    public TableDataInfo<WrongQuestionCollectionVo> queryWrongQuestionCollection(WrongQuestionCollectionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = buildQueryWrapper(bo);
        Page<WrongQuestionCollectionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            WrongQuestionCollectionDetailBo detailBo = new WrongQuestionCollectionDetailBo();
            detailBo.setCollectionList(result.getRecords().stream().map(WrongQuestionCollectionVo::getWrongQuestionCollectionId).toList());
            Map<Long, List<WrongQuestionCollectionDetailVo>> detailMap = wrongQuestionCollectionDetailService.queryList(detailBo)
                .stream().collect(Collectors.groupingBy(WrongQuestionCollectionDetailVo::getWrongQuestionCollectionId));
            result.getRecords().forEach(i -> {
                List<WrongQuestionCollectionDetailVo> detailVos = detailMap.get(i.getWrongQuestionCollectionId());
                if (CollUtil.isNotEmpty(detailVos)) {
                    Set<Long> parentSet = detailVos.stream().map(WrongQuestionCollectionDetailVo::getQuestionPid)
                        .filter(j -> j != 0).collect(Collectors.toSet());
                    i.setQuestionCount(detailVos.size() - parentSet.size());
                }
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询错题合集列表
     */
    @Override
    public List<WrongQuestionCollectionVo> queryList(WrongQuestionCollectionBo bo) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WrongQuestionCollection> buildQueryWrapper(WrongQuestionCollectionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WrongQuestionCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, WrongQuestionCollection::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCollectionStatus() != null, WrongQuestionCollection::getCollectionStatus, bo.getCollectionStatus());
        lqw.eq(bo.getCollectionType() != null, WrongQuestionCollection::getCollectionType, bo.getCollectionType());
        lqw.like(StringUtils.isNotBlank(bo.getRemark()), WrongQuestionCollection::getRemark, bo.getRemark());
        lqw.orderByAsc(WrongQuestionCollection::getCollectionStatus)
            .orderByDesc(WrongQuestionCollection::getUpdateTime);
        return lqw;
    }

    /**
     * 新增错题合集
     */
    @Transactional
    @Override
    public Boolean addCollection(WrongQuestionCollectionBo bo) {
        WrongQuestionCollection collection = MapstructUtils.convert(bo, WrongQuestionCollection.class);
        boolean flag = baseMapper.insert(collection) > 0;
        if (flag) {
            List<WrongQuestionCollectionDetail> collectionDetailList = new ArrayList<>();
            AtomicInteger sort = new AtomicInteger();
            AtomicInteger number = new AtomicInteger();
            bo.getWrongQuestionBoList().forEach(wrongQuestionBo -> {
                WrongQuestionCollectionDetail detail = new WrongQuestionCollectionDetail();
                assert collection != null;
                detail.setWrongQuestionCollectionId(collection.getWrongQuestionCollectionId());
                detail.setQuestionId(wrongQuestionBo.getQuestionId());
                detail.setQuestionNo(String.valueOf(number.incrementAndGet()));
                detail.setQuestionPid(0L);
                detail.setSort(sort.incrementAndGet());
                setQuestionChildren(collectionDetailList, sort, detail, wrongQuestionBo);
                if (CollUtil.isEmpty(wrongQuestionBo.getChildren())) {
                    collectionDetailList.add(detail);
                }
            });
            wrongQuestionCollectionDetailService.batchSaveCollectionDetail(collectionDetailList);
        }
        return flag;
    }

    @Transactional
    @Override
    public Boolean wrongQuestionRevise(WrongQuestionReviseBo bo) {
        WrongQuestionCollection collection = baseMapper.selectById(bo.getWrongQuestionCollectionId());
        if (Objects.isNull(collection)) {
            return Boolean.FALSE;
        }
        List<WrongQuestionCollectionDetailVo> detailVos = wrongQuestionCollectionDetailService.queryByCollectionId(collection.getWrongQuestionCollectionId());
        if (CollUtil.isEmpty(detailVos)) {
            return Boolean.FALSE;
        }

        Map<Long, WrongQuestionCollectionDetailBo> reviseBoMap = bo.getCollectionDetailBoList().stream()
            .collect(Collectors.toMap(WrongQuestionCollectionDetailBo::getQuestionId, Function.identity(), (v1, v2) -> v1));
        List<WrongQuestionCollectionDetail> detailList = new ArrayList<>();
        detailVos.forEach(i -> {
            WrongQuestionCollectionDetailBo detailBo = reviseBoMap.get(i.getQuestionId());
            if (Objects.nonNull(detailBo)) {
                i.setAnswerResult(detailBo.getAnswerResult());
                i.setUpdateTime(new Date());
                WrongQuestionCollectionDetail convert = MapstructUtils.convert(i, WrongQuestionCollectionDetail.class);
                if (Objects.nonNull(convert)) {
                    detailList.add(convert);
                }
            }
        });
        if (CollUtil.isNotEmpty(detailList)) {
            wrongQuestionCollectionDetailService.batchUpdate(detailList);
        }

        collection.setCollectionStatus(1);
        collection.setUpdateTime(new Date());
        collection.setUpdateBy(LoginHelper.getStudentId());
        collection.setReviseScreenshots(bo.getReviseScreenshots());
        baseMapper.updateById(collection);

        Map<Long, WrongQuestionCollectionDetail> detailMap = detailList.stream()
            .collect(Collectors.toMap(WrongQuestionCollectionDetail::getQuestionId, Function.identity()));
        if (!detailMap.isEmpty()) {
            // 更新错题记录
            WrongQuestionRecordBo recordBo = new WrongQuestionRecordBo();
            recordBo.setStudentId(LoginHelper.getStudentId());
            recordBo.setQuestionIds(new ArrayList<>(detailMap.keySet()));
            List<WrongQuestionRecordVo> recordVos = wrongQuestionRecordService.queryList(recordBo);
            if (CollUtil.isNotEmpty(recordVos)) {
                recordVos.forEach(i -> {
                    i.setReviseStatus(1);
                    i.setReviseScreenshots(bo.getReviseScreenshots());
                    i.setReviseTime(new Date());
                });
                wrongQuestionRecordService.batchUpdate(recordVos);
            }

            WrongQuestionRecordNewBo recordNewBo = new WrongQuestionRecordNewBo();
            recordNewBo.setStudentId(LoginHelper.getStudentId());
            recordNewBo.setQuestionIds(new ArrayList<>(detailMap.keySet()));
            List<WrongQuestionRecordNewVo> recordNewVos = wrongQuestionRecordNewService.queryList(recordNewBo);
            if (CollUtil.isNotEmpty(recordNewVos)) {
                recordNewVos.forEach(i -> {
                    i.setReviseStatus(1);
                    i.setReviseScreenshots(bo.getReviseScreenshots());
                    i.setReviseTime(new Date());
                });
                wrongQuestionRecordNewService.batchUpdate(recordNewVos);
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public WrongQuestionCollectionInfoVo queryCollectionInfo(Long wrongQuestionCollectionId) {
        WrongQuestionCollectionInfoVo infoVo = new WrongQuestionCollectionInfoVo();
        WrongQuestionCollectionVo collectionVo = queryById(wrongQuestionCollectionId);
        if (Objects.isNull(collectionVo)) {
            return infoVo;
        }
        infoVo.setWrongQuestionCollectionVo(collectionVo);

        List<WrongQuestionCollectionDetailVo> detailVos = wrongQuestionCollectionDetailService.queryByCollectionId(wrongQuestionCollectionId);
        infoVo.setWrongQuestionCollectionDetailVos(detailVos);

        WrongQuestionRecordBo bo = new WrongQuestionRecordBo();
        bo.setWrongQuestionCollectionId(wrongQuestionCollectionId);
        List<WrongQuestionRecordV2Vo> recordV2VoList = wrongQuestionRecordService.mergeSubQuestionsByRelation(bo, null, Boolean.FALSE);
        infoVo.setWrongQuestionRecordV2Vos(recordV2VoList);

        return infoVo;
    }

    private void setQuestionChildren(List<WrongQuestionCollectionDetail> detailList, AtomicInteger sort,
                                     WrongQuestionCollectionDetail parent, WrongQuestionBo questionBo) {
        if (CollUtil.isEmpty(questionBo.getChildren())) {
            return;
        }
        AtomicInteger number = new AtomicInteger();
        questionBo.getChildren().forEach(child -> {
            WrongQuestionCollectionDetail detail = new WrongQuestionCollectionDetail();
            detail.setWrongQuestionCollectionId(parent.getWrongQuestionCollectionId());
            detail.setQuestionId(child.getQuestionId());
            detail.setQuestionPid(parent.getQuestionId());
            detail.setQuestionNo(parent.getQuestionNo() + "." + number.incrementAndGet());
            detail.setSort(sort.incrementAndGet());
            detailList.add(detail);
        });
    }

    /**
     * 修改错题合集
     */
    @Override
    public Boolean updateByBo(WrongQuestionCollectionBo bo) {
        WrongQuestionCollection update = BeanUtil.toBean(bo, WrongQuestionCollection.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WrongQuestionCollection entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除错题合集
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据学生ID查询错题合集列表
     */
    @Override
    public List<WrongQuestionCollectionVo> queryByStudentId(Long studentId) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(WrongQuestionCollection::getStudentId, studentId);
        lqw.orderByDesc(WrongQuestionCollection::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据学生ID和类型查询错题合集列表
     */
    @Override
    public List<WrongQuestionCollectionVo> queryByStudentIdAndType(Long studentId, Integer collectionType) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(WrongQuestionCollection::getStudentId, studentId);
        lqw.eq(WrongQuestionCollection::getCollectionType, collectionType);
        lqw.orderByDesc(WrongQuestionCollection::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 更新错题合集状态
     */
    @Override
    public Boolean updateCollectionStatus(Long wrongQuestionCollectionId, Integer collectionStatus) {
        WrongQuestionCollection update = new WrongQuestionCollection();
        update.setWrongQuestionCollectionId(wrongQuestionCollectionId);
        update.setCollectionStatus(collectionStatus);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 统计学生错题合集数量
     */
    @Override
    public int countByStudentId(Long studentId) {
        return baseMapper.countByStudentId(studentId);
    }

    /**
     * 统计学生指定状态的错题合集数量
     */
    @Override
    public int countByStudentIdAndStatus(Long studentId, Integer collectionStatus) {
        return baseMapper.countByStudentIdAndStatus(studentId, collectionStatus);
    }

}
